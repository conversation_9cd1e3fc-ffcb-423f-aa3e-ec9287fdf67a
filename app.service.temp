[Unit]
Description=T.J. Sokol Suchdol Web Application
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/api --disable-https --http-port=80 --log-level=info
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=sokol-app

# Security settings
PrivateTmp=true
ProtectSystem=full
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
