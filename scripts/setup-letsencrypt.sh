#!/bin/bash

# Skript pro nastavení Let's Encrypt certifikátů pro T.J. Sokol Suchdol
# Použití: ./setup-letsencrypt.sh [doména]

set -e

DOMAIN=${1:-"sokolsuchdol.cz"}
EMAIL="<EMAIL>"

echo "=== Nastavení Let's Encrypt certifikátů pro $DOMAIN ==="

# <PERSON><PERSON><PERSON><PERSON>, zda běží jako root
if [[ $EUID -ne 0 ]]; then
   echo "Tento skript musí být spuštěn jako root (sudo)" 
   exit 1
fi

# Aktualizace systému
echo "Aktualizuji systém..."
apt-get update

# Instalace Certbot
echo "Instaluji Certbot..."
apt-get install -y certbot

# Zastavení aplikace pro získání certifikátu
echo "Zastavuji aplikaci..."
systemctl stop app.service || true

# Získání certifikátu
echo "Získávám Let's Encrypt certifikát pro $DOMAIN..."
certbot certonly --standalone \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --domains $DOMAIN,www.$DOMAIN

# Nastavení oprávnění pro certifikáty
echo "Nastavuji oprávnění pro certifikáty..."
chmod 644 /etc/letsencrypt/live/$DOMAIN/fullchain.pem
chmod 600 /etc/letsencrypt/live/$DOMAIN/privkey.pem

# Vytvoření hook skriptu pro restart aplikace po obnovení certifikátu
echo "Vytvářím hook skript pro automatické obnovení..."
cat > /etc/letsencrypt/renewal-hooks/deploy/restart-app.sh << 'EOF'
#!/bin/bash
systemctl restart app.service
EOF

chmod +x /etc/letsencrypt/renewal-hooks/deploy/restart-app.sh

# Nastavení automatického obnovení
echo "Nastavuji automatické obnovení certifikátů..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

# Spuštění aplikace
echo "Spouštím aplikaci s HTTPS..."
systemctl start app.service

# Kontrola stavu
echo "Kontroluji stav aplikace..."
sleep 5
systemctl status app.service

echo ""
echo "=== Nastavení dokončeno! ==="
echo "Certifikáty jsou uloženy v: /etc/letsencrypt/live/$DOMAIN/"
echo "Aplikace nyní běží na HTTPS portu 443"
echo "HTTP port 80 přesměrovává na HTTPS"
echo ""
echo "Pro kontrolu certifikátu použijte:"
echo "  openssl x509 -in /etc/letsencrypt/live/$DOMAIN/fullchain.pem -text -noout"
echo ""
echo "Pro test HTTPS připojení:"
echo "  curl -I https://$DOMAIN"
