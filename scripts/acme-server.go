package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
)

func main() {
	if len(os.Args) != 2 {
		log.Fatal("Usage: acme-server <port>")
	}
	
	port := os.Args[1]
	
	// Serve ACME challenges from /var/www/html
	http.Handle("/.well-known/acme-challenge/", http.StripPrefix("/.well-known/acme-challenge/", http.FileServer(http.Dir("/var/www/html/.well-known/acme-challenge"))))
	
	// Redirect all other requests to HTTPS
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		httpsURL := "https://" + r.Host + r.RequestURI
		http.Redirect(w, r, httpsURL, http.StatusMovedPermanently)
	})
	
	addr := fmt.Sprintf(":%s", port)
	log.Printf("Starting ACME server on %s", addr)
	log.Fatal(http.ListenAndServe(addr, nil))
}
