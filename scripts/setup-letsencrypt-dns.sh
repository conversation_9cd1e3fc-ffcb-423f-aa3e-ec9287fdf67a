#!/bin/bash

# Skript pro nastavení Let's Encrypt certifikátů pomocí DNS challenge
# Použití: ./setup-letsencrypt-dns.sh [doména]

set -e

DOMAIN=${1:-"sokolsuchdol.cz"}
EMAIL="<EMAIL>"

echo "=== Nastavení Let's Encrypt certifikátů pomocí DNS challenge pro $DOMAIN ==="

# Kontrola, zda b<PERSON><PERSON><PERSON> jako root
if [[ $EUID -ne 0 ]]; then
   echo "Tento skript musí být spuštěn jako root (sudo)" 
   exit 1
fi

echo "DŮLEŽITÉ: Před pokračováním musíte přidat následující DNS TXT záznamy:"
echo ""
echo "1. Název: _acme-challenge.$DOMAIN"
echo "   Typ: TXT"
echo "   Hodnota: [bude zobrazena v dal<PERSON><PERSON><PERSON> kroku]"
echo ""
echo "2. Název: _acme-challenge.www.$DOMAIN"
echo "   Typ: TXT"
echo "   Hodnota: [bude zobrazena v dalším kroku]"
echo ""
echo "Pokračujte pouze po přidání těchto záznamů do DNS!"
echo ""

read -p "Pokračovat s generováním certifikátu? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Ukončeno uživatelem."
    exit 1
fi

# Zastavení aplikace pro získání certifikátu
echo "Zastavuji aplikaci..."
systemctl stop app.service || true

# Získání certifikátu pomocí DNS challenge
echo "Spouštím DNS challenge pro $DOMAIN..."
echo "POZOR: Budete muset přidat DNS TXT záznamy ručně!"

certbot certonly --manual --preferred-challenges dns \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --domains $DOMAIN,www.$DOMAIN

# Kontrola, zda byly certifikáty vytvořeny
if [ ! -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
    echo "CHYBA: Certifikáty nebyly vytvořeny!"
    echo "Spouštím aplikaci s původními certifikáty..."
    systemctl start app.service
    exit 1
fi

# Nastavení oprávnění pro certifikáty
echo "Nastavuji oprávnění pro certifikáty..."
chmod 644 /etc/letsencrypt/live/$DOMAIN/fullchain.pem
chmod 600 /etc/letsencrypt/live/$DOMAIN/privkey.pem

# Aktualizace app.service pro použití Let's Encrypt certifikátů
echo "Aktualizuji app.service pro Let's Encrypt certifikáty..."
cat > /etc/systemd/system/app.service << EOF
[Unit]
Description=T.J. Sokol Suchdol Web Application
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/api --http-port=8080 --https-port=443 --log-level=info --cert=/etc/letsencrypt/live/$DOMAIN/fullchain.pem --key=/etc/letsencrypt/live/$DOMAIN/privkey.pem
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=sokol-app

# Security settings
PrivateTmp=true
ProtectSystem=full
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF

# Vytvoření hook skriptu pro restart aplikace po obnovení certifikátu
echo "Vytvářím hook skript pro automatické obnovení..."
mkdir -p /etc/letsencrypt/renewal-hooks/deploy
cat > /etc/letsencrypt/renewal-hooks/deploy/restart-app.sh << 'EOF'
#!/bin/bash
systemctl restart app.service
EOF

chmod +x /etc/letsencrypt/renewal-hooks/deploy/restart-app.sh

# Nastavení automatického obnovení
echo "Nastavuji automatické obnovení certifikátů..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

# Reload systemd a restart aplikace
systemctl daemon-reload
systemctl start app.service

# Kontrola stavu
echo "Kontroluji stav aplikace..."
sleep 5
systemctl status app.service

echo ""
echo "=== Nastavení dokončeno! ==="
echo "Certifikáty jsou uloženy v: /etc/letsencrypt/live/$DOMAIN/"
echo "Aplikace nyní běží s Let's Encrypt certifikáty"
echo ""
echo "Pro kontrolu certifikátu použijte:"
echo "  openssl x509 -in /etc/letsencrypt/live/$DOMAIN/fullchain.pem -text -noout"
echo ""
echo "Pro test HTTPS připojení:"
echo "  curl -I https://$DOMAIN"
