[Unit]
Description=T.J. Sokol Suchdol Web Application
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/api --http-port=8080 --https-port=443 --log-level=info --cert=/etc/letsencrypt/live/sokolsuchdol.cz/fullchain.pem --key=/etc/letsencrypt/live/sokolsuchdol.cz/privkey.pem
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=sokol-app

# Security settings
PrivateTmp=true
ProtectSystem=full
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
