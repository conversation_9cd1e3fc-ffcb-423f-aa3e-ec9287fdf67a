# ==================================================================================== #
# VARIABLES
# ==================================================================================== #

APP_VERSION ?= v1.0.0
APP_NAME ?= sokol
MOD_NAME := $(shell go list -m)
BIN_DIR := ./bin
LINUX_DIR := $(BIN_DIR)/linux_amd64
SERVER_USER ?= root
SERVER_HOST ?= ***************
SERVER_DIR ?= /home/<USER>/app
LOG_LEVEL ?= info
PORT ?= 8080

# ==================================================================================== #
# HELP
# ==================================================================================== #

## help: zobrazí nápovědu
.PHONY: help
help:
	@echo 'Použití:'
	@sed -n 's/^##//p' ${MAKEFILE_LIST} | column -t -s ':' | sed -e 's/^/ /'

# ==================================================================================== #
# DEVELOPMENT
# ==================================================================================== #

## run: spustí aplikaci lokálně (pouze HTTP pro vývoj)
.PHONY: run
run:
	go run ./cmd/api/main.go --log-level=$(LOG_LEVEL) --http-port=$(PORT) --disable-https

## run-https: spustí aplikaci lokálně s HTTPS (vyžaduje certifikáty)
.PHONY: run-https
run-https:
	go run ./cmd/api/main.go --log-level=$(LOG_LEVEL) --http-port=80 --https-port=443 --cert=./certs/cert.pem --key=./certs/key.pem

## generate-certs: vygeneruje self-signed certifikáty pro vývoj
.PHONY: generate-certs
generate-certs:
	@echo "Generuji self-signed certifikáty pro vývoj..."
	@mkdir -p certs
	openssl req -x509 -newkey rsa:4096 -keyout certs/key.pem -out certs/cert.pem -days 365 -nodes \
		-subj "/C=CZ/ST=Prague/L=Prague/O=T.J. Sokol Suchdol/OU=IT Department/CN=localhost"
	@echo "Certifikáty vygenerovány v adresáři certs/"
	@echo "Pro HTTPS na localhost použijte: make run-https"

## docker-build: sestaví Docker image
.PHONY: docker-build
docker-build:
	docker compose build

## docker-up: spustí aplikaci v Docker kontejneru
.PHONY: docker-up
docker-up:
	docker compose up -d

## docker-down: zastaví aplikaci v Docker kontejneru
.PHONY: docker-down
docker-down:
	docker compose down

## docker-logs: zobrazí logy aplikace v Docker kontejneru
.PHONY: docker-logs
docker-logs:
	docker compose logs -f

# ==================================================================================== #
# BUILD
# ==================================================================================== #

## build: sestaví všechny aplikační entrypoints
.PHONY: build
build:
	go mod verify
	@echo "Sestavuji aplikaci..."
	@mkdir -p $(BIN_DIR)
	go build -ldflags='-s' -ldflags="-X '$(MOD_NAME)/internal/kernel.Version=$(APP_VERSION)' -X '$(MOD_NAME)/internal/kernel.AppName=$(APP_NAME)' " -o=$(BIN_DIR)/api ./cmd/api
	@echo "Aplikace úspěšně sestavena: $(BIN_DIR)/api"

## build-linux: sestaví aplikaci pro Linux
.PHONY: build-linux
build-linux:
	go mod verify
	@echo "Sestavuji aplikaci pro Linux..."
	@mkdir -p $(LINUX_DIR)
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -ldflags='-s' -ldflags="-X '$(MOD_NAME)/internal/kernel.Version=$(APP_VERSION)' -X '$(MOD_NAME)/internal/kernel.AppName=$(APP_NAME)' " -o=$(LINUX_DIR)/api ./cmd/api
	@echo "Aplikace úspěšně sestavena: $(LINUX_DIR)/api"

# ==================================================================================== #
# QUALITY CONTROL
# ==================================================================================== #

## dev-env: připraví vývojové prostředí v Docker kontejneru
.PHONY: dev-env
dev-env:
	docker compose build dev
	docker compose up -d dev

## dev-stop: zastaví vývojové prostředí v Docker kontejneru
.PHONY: dev-stop
dev-stop:
	docker compose stop dev

## dev-shell: spustí shell ve vývojovém kontejneru
.PHONY: dev-shell
dev-shell: dev-env
	docker compose exec dev sh

## fmt: formátuje zdrojový kód v Docker kontejneru
.PHONY: fmt
fmt: dev-env
	docker compose exec dev go fmt ./...

## vet: kontroluje zdrojový kód v Docker kontejneru
.PHONY: vet
vet: dev-env
	docker compose exec dev go vet ./...

## lint: spustí linter v Docker kontejneru
.PHONY: lint
lint: dev-env
	docker compose exec dev go vet ./...

## test: spustí testy v Docker kontejneru
.PHONY: test
test: dev-env
	docker compose exec dev go test -v ./...

## audit: spustí všechny kontroly kvality v Docker kontejneru
.PHONY: audit
audit: dev-env
	@echo "Spouštím kontrolu kvality kódu v Docker kontejneru..."
	docker compose exec dev sh -c "go fmt ./... && go vet ./... && go test -v ./..."
	@echo "Kontrola kvality kódu dokončena!"

## audit-local: spustí všechny kontroly kvality lokálně (bez Docker)
.PHONY: audit-local
audit-local:
	@echo "Spouštím kontrolu kvality kódu lokálně..."
	go fmt ./...
	go vet ./...
	go test -v ./...
	@echo "Kontrola kvality kódu dokončena!"

# ==================================================================================== #
# DEPLOYMENT
# ==================================================================================== #

## deploy: nasadí aplikaci na server
.PHONY: deploy
deploy: build-linux
	@echo "Kompresuji binární soubor..."
	gzip -c $(LINUX_DIR)/api > $(LINUX_DIR)/api.gz
	@echo "Přenáším soubor na server..."
	scp $(LINUX_DIR)/api.gz $(SERVER_USER)@$(SERVER_HOST):$(SERVER_DIR)/
	@echo "Rozbaluji a nastavuji oprávnění..."
	ssh $(SERVER_USER)@$(SERVER_HOST) "cd $(SERVER_DIR) && gunzip -f api.gz && chmod +x api && chown sokol:sokol api"
	@echo "Restartuji službu..."
	ssh $(SERVER_USER)@$(SERVER_HOST) "systemctl restart app.service"
	@echo "Kontroluji stav služby..."
	ssh $(SERVER_USER)@$(SERVER_HOST) "systemctl status app.service"
	@echo "Nasazení dokončeno!"

## deploy-check: zkontroluje stav aplikace na serveru
.PHONY: deploy-check
deploy-check:
	ssh root@$(SERVER_HOST) "systemctl status app.service"
	curl -I https://$(SERVER_HOST)
	curl -I http://$(SERVER_HOST)

## setup-https: nastaví Let's Encrypt certifikáty na serveru
.PHONY: setup-https
setup-https:
	@echo "Přenáším setup skript na server..."
	scp scripts/setup-letsencrypt.sh $(SERVER_USER)@$(SERVER_HOST):/tmp/
	@echo "Spouštím setup HTTPS na serveru..."
	ssh $(SERVER_USER)@$(SERVER_HOST) "chmod +x /tmp/setup-letsencrypt.sh && /tmp/setup-letsencrypt.sh"
	@echo "HTTPS nastavení dokončeno!"

## deploy-logs: zobrazí logy aplikace na serveru
.PHONY: deploy-logs
deploy-logs:
	ssh root@$(SERVER_HOST) "journalctl -u app.service -f"