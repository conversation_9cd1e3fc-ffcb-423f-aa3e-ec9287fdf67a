package main

import (
	"embed"
	"flag"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"time"
)

//go:embed index.html
var indexHTML []byte

//go:embed styles.css
var stylesCSS []byte

//go:embed images
var images embed.FS

// handleIndex processes requests for the main page
func handleIndex(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		slog.Info("Not found", "path", r.URL.Path, "method", r.Method, "remote_addr", r.RemoteAddr)
		http.NotFound(w, r)
		return
	}

	slog.Debug("Serving index page", "remote_addr", r.<PERSON>dd<PERSON>, "user_agent", r.User<PERSON>())
	w.<PERSON>er().Set("Content-Type", "text/html")
	_, err := w.Write(indexHTML)
	if err != nil {
		slog.Error("Failed to write response", "error", err, "remote_addr", r.<PERSON>mote<PERSON>ddr)
	}
}

// handleCSS processes requests for the CSS file
func handleCSS(w http.ResponseWriter, r *http.Request) {
	slog.Debug("Serving CSS file", "remote_addr", r.RemoteAddr)
	w.Header().Set("Content-Type", "text/css")
	w.Header().Set("Cache-Control", "public, max-age=3600") // Cache for 1 hour
	_, err := w.Write(stylesCSS)
	if err != nil {
		slog.Error("Failed to write CSS response", "error", err, "remote_addr", r.RemoteAddr)
	}
}

func main() {
	// Logging configuration
	logLevel := flag.String("log-level", "info", "Logging level (debug, info, warn, error)")
	port := flag.String("port", "80", "Port for HTTP server")
	flag.Parse()

	// Set logging level
	var level slog.Level
	switch *logLevel {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	// Configure slog
	logHandler := slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level:     level,
		AddSource: true,
	})
	slog.SetDefault(slog.New(logHandler))

	// Register handlers
	http.HandleFunc("/", handleIndex)
	http.HandleFunc("/styles.css", handleCSS)
	http.Handle("/images/", http.FileServer(http.FS(images)))

	// Start the server
	addr := fmt.Sprintf(":%s", *port)
	slog.Info("Starting HTTP server", "address", addr)

	// Create server with timeouts
	server := &http.Server{
		Addr:         addr,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start the server and handle errors
	err := server.ListenAndServe()
	if err != nil && err != http.ErrServerClosed {
		slog.Error("Server failed", "error", err)
		os.Exit(1)
	}
}
