package main

import (
	"context"
	"crypto/tls"
	"embed"
	"flag"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"
)

//go:embed index.html
var indexHTML []byte

//go:embed styles.css
var stylesCSS []byte

//go:embed images
var images embed.FS

// setSecurityHeaders sets security headers for HTTPS
func setSecurityHeaders(w http.ResponseWriter, r *http.Request) {
	// Only set security headers for HTTPS requests
	if r.TLS != nil {
		w.Header().Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		w.Header().Set("X-Content-Type-Options", "nosniff")
		w.Header().Set("X-Frame-Options", "DENY")
		w.<PERSON>er().Set("X-XSS-Protection", "1; mode=block")
		w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
	}
}

// handleIndex processes requests for the main page
func handleIndex(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		slog.Info("Not found", "path", r.URL.Path, "method", r.Method, "remote_addr", r.RemoteAddr)
		http.NotFound(w, r)
		return
	}

	setSecurityHeaders(w, r)
	slog.Debug("Serving index page", "remote_addr", r.RemoteAddr, "user_agent", r.UserAgent())
	w.Header().Set("Content-Type", "text/html")
	_, err := w.Write(indexHTML)
	if err != nil {
		slog.Error("Failed to write response", "error", err, "remote_addr", r.RemoteAddr)
	}
}

// handleCSS processes requests for the CSS file
func handleCSS(w http.ResponseWriter, r *http.Request) {
	setSecurityHeaders(w, r)
	slog.Debug("Serving CSS file", "remote_addr", r.RemoteAddr)
	w.Header().Set("Content-Type", "text/css")
	w.Header().Set("Cache-Control", "public, max-age=3600") // Cache for 1 hour
	_, err := w.Write(stylesCSS)
	if err != nil {
		slog.Error("Failed to write CSS response", "error", err, "remote_addr", r.RemoteAddr)
	}
}

// redirectToHTTPS redirects HTTP requests to HTTPS
func redirectToHTTPS(w http.ResponseWriter, r *http.Request) {
	httpsURL := "https://" + r.Host + r.RequestURI
	slog.Debug("Redirecting to HTTPS", "from", r.URL.String(), "to", httpsURL, "remote_addr", r.RemoteAddr)
	http.Redirect(w, r, httpsURL, http.StatusMovedPermanently)
}

func main() {
	// Command line flags
	logLevel := flag.String("log-level", "info", "Logging level (debug, info, warn, error)")
	httpPort := flag.String("http-port", "80", "Port for HTTP server")
	httpsPort := flag.String("https-port", "443", "Port for HTTPS server")
	certFile := flag.String("cert", "/etc/letsencrypt/live/sokolsuchdol.cz/fullchain.pem", "Path to TLS certificate file")
	keyFile := flag.String("key", "/etc/letsencrypt/live/sokolsuchdol.cz/privkey.pem", "Path to TLS private key file")
	httpsOnly := flag.Bool("https-only", false, "Run only HTTPS server (no HTTP redirect)")
	disableHTTPS := flag.Bool("disable-https", false, "Disable HTTPS and run only HTTP (for development)")
	flag.Parse()

	// Set logging level
	var level slog.Level
	switch *logLevel {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	// Configure slog
	logHandler := slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
		Level:     level,
		AddSource: true,
	})
	slog.SetDefault(slog.New(logHandler))

	// Register handlers
	http.HandleFunc("/", handleIndex)
	http.HandleFunc("/styles.css", handleCSS)
	http.Handle("/images/", http.FileServer(http.FS(images)))

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Channel to listen for interrupt signal
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	var wg sync.WaitGroup

	// Start servers based on configuration
	if *disableHTTPS {
		// Development mode - HTTP only
		startHTTPServer(ctx, &wg, *httpPort)
	} else {
		// Production mode - HTTPS with optional HTTP redirect
		if !*httpsOnly {
			// Start HTTP server for redirect
			startHTTPRedirectServer(ctx, &wg, *httpPort)
		}
		// Start HTTPS server
		startHTTPSServer(ctx, &wg, *httpsPort, *certFile, *keyFile)
	}

	// Wait for interrupt signal
	<-c
	slog.Info("Shutting down servers...")
	cancel()

	// Wait for all servers to shutdown
	wg.Wait()
	slog.Info("All servers stopped")
}

// startHTTPServer starts HTTP server (for development)
func startHTTPServer(ctx context.Context, wg *sync.WaitGroup, port string) {
	wg.Add(1)
	go func() {
		defer wg.Done()

		addr := fmt.Sprintf(":%s", port)
		server := &http.Server{
			Addr:         addr,
			ReadTimeout:  15 * time.Second,
			WriteTimeout: 15 * time.Second,
			IdleTimeout:  60 * time.Second,
		}

		slog.Info("Starting HTTP server", "address", addr)

		go func() {
			if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				slog.Error("HTTP server failed", "error", err)
			}
		}()

		<-ctx.Done()
		slog.Info("Shutting down HTTP server")

		shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := server.Shutdown(shutdownCtx); err != nil {
			slog.Error("HTTP server shutdown failed", "error", err)
		} else {
			slog.Info("HTTP server stopped")
		}
	}()
}

// startHTTPRedirectServer starts HTTP server that redirects to HTTPS
func startHTTPRedirectServer(ctx context.Context, wg *sync.WaitGroup, port string) {
	wg.Add(1)
	go func() {
		defer wg.Done()

		addr := fmt.Sprintf(":%s", port)

		// Create a new mux for redirect server
		redirectMux := http.NewServeMux()
		redirectMux.HandleFunc("/", redirectToHTTPS)

		server := &http.Server{
			Addr:         addr,
			Handler:      redirectMux,
			ReadTimeout:  15 * time.Second,
			WriteTimeout: 15 * time.Second,
			IdleTimeout:  60 * time.Second,
		}

		slog.Info("Starting HTTP redirect server", "address", addr)

		go func() {
			if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				slog.Error("HTTP redirect server failed", "error", err)
			}
		}()

		<-ctx.Done()
		slog.Info("Shutting down HTTP redirect server")

		shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := server.Shutdown(shutdownCtx); err != nil {
			slog.Error("HTTP redirect server shutdown failed", "error", err)
		} else {
			slog.Info("HTTP redirect server stopped")
		}
	}()
}

// startHTTPSServer starts HTTPS server with TLS configuration
func startHTTPSServer(ctx context.Context, wg *sync.WaitGroup, port, certFile, keyFile string) {
	wg.Add(1)
	go func() {
		defer wg.Done()

		addr := fmt.Sprintf(":%s", port)

		// Check if certificate files exist
		if _, err := os.Stat(certFile); os.IsNotExist(err) {
			slog.Error("Certificate file not found", "file", certFile)
			return
		}
		if _, err := os.Stat(keyFile); os.IsNotExist(err) {
			slog.Error("Private key file not found", "file", keyFile)
			return
		}

		// Configure TLS
		tlsConfig := &tls.Config{
			MinVersion:               tls.VersionTLS12,
			CurvePreferences:         []tls.CurveID{tls.CurveP521, tls.CurveP384, tls.CurveP256},
			PreferServerCipherSuites: true,
			CipherSuites: []uint16{
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			},
		}

		server := &http.Server{
			Addr:         addr,
			TLSConfig:    tlsConfig,
			ReadTimeout:  15 * time.Second,
			WriteTimeout: 15 * time.Second,
			IdleTimeout:  60 * time.Second,
		}

		slog.Info("Starting HTTPS server", "address", addr, "cert", certFile, "key", keyFile)

		go func() {
			if err := server.ListenAndServeTLS(certFile, keyFile); err != nil && err != http.ErrServerClosed {
				slog.Error("HTTPS server failed", "error", err)
			}
		}()

		<-ctx.Done()
		slog.Info("Shutting down HTTPS server")

		shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := server.Shutdown(shutdownCtx); err != nil {
			slog.Error("HTTPS server shutdown failed", "error", err)
		} else {
			slog.Info("HTTPS server stopped")
		}
	}()
}