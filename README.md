# T.<PERSON><PERSON><PERSON> - Webová Aplikace

Jednoduchá webová aplikace pro T.J. <PERSON>, která poskytuje základní informace o organizaci, nadcházejících akcích a kontaktní údaje.

## Popis projektu

Tato aplikace je napsána v jazyce Go a poskytuje jednoduchou webovou stránku s následujícími funkcemi:

- Základní informace o T.J. Sokol Suchdol - Sedlec
- Kalendář nadcházejících akcí (integrovaný Google Calendar)
- Kontaktní informace včetně e-mailových adres
- Odkaz na mateřskou organizaci Česká obec sokolská

Aplikace používá embedded soubory pro HTML a obrázky, což umožňuje distribuci jako jediný binární soubor bez závislostí na externích souborech.

## Struktura projektu

```
.
├── README.md                 # Tento soubor
├── LICENSE                   # Licenční soubor
├── app.service               # Konfigurační soubor pro systemd službu
├── bin/                      # Adresář pro zkompilované binární soubory
├── cmd/
│   └── api/                  # Hlavní aplikace
│       ├── images/           # Obrázky použité na webové stránce
│       │   ├── falcon.jpg
│       │   └── sokol_symbol_S_p_RGB.png
│       ├── index.html        # HTML šablona webové stránky
│       └── main.go           # Hlavní zdrojový kód aplikace
└── embedded.go               # Pomocný soubor pro embedded soubory
```

## Požadavky

- Go 1.21 nebo novější

## Instalace a spuštění

### Lokální vývoj

1. Naklonujte repozitář:
   ```
   git clone <URL_repozitáře>
   cd sokol
   ```

2. Spusťte aplikaci:
   ```
   go run cmd/api/main.go
   ```

3. Otevřete webový prohlížeč a přejděte na adresu `http://localhost:80`

### Parametry příkazové řádky

Aplikace podporuje následující parametry příkazové řádky:

#### HTTP/HTTPS konfigurace
- `--http-port=<port>` - Port pro HTTP server (výchozí: 80)
- `--https-port=<port>` - Port pro HTTPS server (výchozí: 443)
- `--cert=<path>` - Cesta k SSL certifikátu (výchozí: Let's Encrypt cesta)
- `--key=<path>` - Cesta k privátnímu klíči (výchozí: Let's Encrypt cesta)
- `--https-only` - Spustit pouze HTTPS server (bez HTTP redirect)
- `--disable-https` - Zakázat HTTPS a spustit pouze HTTP (pro vývoj)

#### Ostatní
- `--log-level=<level>` - Úroveň logování (debug, info, warn, error; výchozí: info)

#### Příklady použití:

**Vývoj (pouze HTTP):**
```bash
go run cmd/api/main.go --disable-https --http-port=8080 --log-level=debug
```

**Produkce (HTTPS + HTTP redirect):**
```bash
./api --http-port=80 --https-port=443 --log-level=info
```

**Pouze HTTPS:**
```bash
./api --https-only --https-port=443
```

## HTTPS Konfigurace

Aplikace podporuje HTTPS s automatickým přesměrováním z HTTP. Pro detailní návod viz [docs/HTTPS_SETUP.md](docs/HTTPS_SETUP.md).

### Rychlé nastavení pro produkci

1. **Automatická instalace Let's Encrypt certifikátů:**
   ```bash
   sudo ./scripts/setup-letsencrypt.sh sokolsuchdol.cz
   ```

2. **Aplikace automaticky běží na HTTPS portu 443 a přesměrovává HTTP (port 80) na HTTPS**

### Vývoj s self-signed certifikáty

```bash
# Vygenerování certifikátů pro localhost
make generate-certs

# Spuštění s HTTPS
make run-https
```

## Sestavení pro produkci

### Sestavení pro Linux

```
GOOS=linux GOARCH=amd64 go build -o bin/api cmd/api/main.go
```

### Sestavení pro Windows

```
GOOS=windows GOARCH=amd64 go build -o bin/api.exe cmd/api/main.go
```

### Sestavení pro macOS

```
GOOS=darwin GOARCH=amd64 go build -o bin/api cmd/api/main.go
```

## Nasazení na server

1. Sestavte aplikaci pro cílovou platformu (viz výše)

2. Zkomprimujte binární soubor pro rychlejší přenos:
   ```
   gzip -c bin/api > bin/api.gz
   ```

3. Přeneste zkomprimovaný soubor na server:
   ```
   scp bin/api.gz user@server:/home/
   ```

4. Na serveru rozbalte soubor a nastavte oprávnění:
   ```
   gunzip -f api.gz
   chmod +x api
   ```

### Použití systemd služby (doporučeno)

Pro automatické spuštění aplikace při startu serveru a její automatické restartování v případě selhání použijte systemd službu:

1. Vytvořte soubor `app.service` s následujícím obsahem:

```
[Unit]
Description=T.J. Sokol Suchdol Web Application
After=network.target

[Service]
Type=simple
User=sokol
Group=sokol
WorkingDirectory=/home/<USER>/app
ExecStart=/home/<USER>/app/api --port=8080 --log-level=info
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=sokol-app

# Security settings
PrivateTmp=true
ProtectSystem=full
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
```

2. Přeneste soubor na server do adresáře `/etc/systemd/system/`:
   ```
   scp app.service user@server:/etc/systemd/system/
   ```

3. Vytvořte uživatele `sokol` a připravte adresářovou strukturu:
   ```
   useradd -m -s /bin/bash sokol
   mkdir -p /home/<USER>/app
   cp /home/<USER>/home/<USER>/app/
   chown -R sokol:sokol /home/<USER>
   chmod +x /home/<USER>/app/api
   ```

4. Nastavte přesměrování portů (neprivilegovaný uživatel nemůže naslouchat na portu 80):
   ```
   # Nainstalujte iptables-persistent pro zachování pravidel po restartu
   apt-get update
   apt-get install -y iptables-persistent

   # Nastavte přesměrování z portu 80 na port 8080
   iptables -t nat -A PREROUTING -p tcp --dport 80 -j REDIRECT --to-port 8080
   iptables-save > /etc/iptables/rules.v4
   ```

5. Povolte a spusťte službu:
   ```
   systemctl daemon-reload
   systemctl enable app.service
   systemctl start app.service
   ```

6. Zkontrolujte stav služby:
   ```
   systemctl status app.service
   ```

### Manuální spuštění

Pokud nechcete použít systemd, můžete aplikaci spustit manuálně:

```
./api > /dev/null 2>&1 &
```

## Údržba a aktualizace

Pro aktualizaci aplikace stačí sestavit novou verzi, přenést ji na server a restartovat běžící instanci.

### Při použití systemd služby

```
# Přeneste novou verzi aplikace na server
scp bin/api.gz user@server:/home/<USER>/app/

# Na serveru
cd /home/<USER>/app
gunzip -f api.gz
chmod +x api
chown sokol:sokol api
systemctl restart app.service

# Zkontrolujte stav služby
systemctl status app.service
```

### Při manuálním spuštění

```
# Přeneste novou verzi aplikace na server
scp bin/api.gz user@server:/home/<USER>/app/

# Na serveru
cd /home/<USER>/app
gunzip -f api.gz
chmod +x api
chown sokol:sokol api

# Spusťte aplikaci jako uživatel sokol
su - sokol -c 'cd ~/app && ./api --port=8080 > /dev/null 2>&1 &'
```

## Licence

Tento projekt je licencován pod [MIT licencí](LICENSE).

## Kontakt

Pro více informací kontaktujte:
- Starosta: <EMAIL>
- Místostarosta: <EMAIL>
- Jednatel: <EMAIL>
- Obecné informace: <EMAIL>
