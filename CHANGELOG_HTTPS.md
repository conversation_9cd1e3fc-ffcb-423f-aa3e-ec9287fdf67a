# HTTPS Implementace - <PERSON><PERSON><PERSON>led změn

## Př<PERSON><PERSON><PERSON> funkce

### 1. HTTPS Server s TLS konfigurací
- Podpora TLS 1.2+ s bezpečnými cipher suites
- <PERSON>ká kontrola existence certifikátů
- Graceful shutdown pro oba servery (HTTP/HTTPS)

### 2. Automatické přesměrování HTTP → HTTPS
- HTTP server automaticky přesměrovává na HTTPS
- Možnost zakázat HTTP server (`--https-only`)
- Možnost zakázat HTTPS pro vývoj (`--disable-https`)

### 3. Bezpečnostn<PERSON> h<PERSON>
- `Strict-Transport-Security` - HSTS pro HTTPS
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

### 4. Let's Encrypt integrace
- Automatický setup skript pro Let's Encrypt
- Výchozí cesty k certifikátům
- <PERSON>k<PERSON> obnovení certifikátů

## Nové parametry příkazové řádky

- `--http-port=80` - Port pro HTTP server
- `--https-port=443` - Port pro HTTPS server
- `--cert=/path/to/cert.pem` - Cesta k SSL certifikátu
- `--key=/path/to/key.pem` - Cesta k privátnímu klíči
- `--https-only` - Pouze HTTPS (bez HTTP redirect)
- `--disable-https` - Pouze HTTP (pro vývoj)

## Nové soubory

### Skripty
- `scripts/setup-letsencrypt.sh` - Automatická instalace Let's Encrypt

### Dokumentace
- `docs/HTTPS_SETUP.md` - Detailní návod pro HTTPS
- `CHANGELOG_HTTPS.md` - Tento soubor

### Adresáře
- `certs/` - Pro lokální certifikáty (ignorováno v git)

## Aktualizované soubory

### `cmd/api/main.go`
- Přidány importy pro TLS, context, signal handling
- Nové funkce: `startHTTPServer`, `startHTTPSServer`, `startHTTPRedirectServer`
- Bezpečnostní hlavičky v `setSecurityHeaders`
- Graceful shutdown s context a WaitGroup

### `Makefile`
- `make run` - HTTP pouze pro vývoj
- `make run-https` - HTTPS s lokálními certifikáty
- `make generate-certs` - Generování self-signed certifikátů
- `make setup-https` - Remote setup Let's Encrypt
- `make deploy-check` - Test HTTPS i HTTP

### `app.service`
- Aktualizované parametry pro HTTPS
- Výchozí cesty k Let's Encrypt certifikátům

### `README.md`
- Nová sekce o HTTPS konfiguraci
- Aktualizované příklady použití
- Odkazy na dokumentaci

### `.gitignore`
- Přidány SSL/TLS soubory (*.pem, *.crt, *.key, atd.)
- Ignorování adresáře `certs/`

## Použití

### Vývoj
```bash
# Pouze HTTP
make run

# HTTPS s self-signed certifikáty
make generate-certs
make run-https
```

### Produkce
```bash
# Automatické nastavení Let's Encrypt
sudo ./scripts/setup-letsencrypt.sh sokolsuchdol.cz

# Nebo manuální nasazení
make deploy
make setup-https
```

## Bezpečnostní vylepšení

1. **TLS konfigurace:**
   - Minimální TLS 1.2
   - Bezpečné cipher suites
   - Preferované server cipher suites

2. **HTTP Security Headers:**
   - HSTS pro vynucení HTTPS
   - Ochrana proti XSS a clickjacking
   - Content type sniffing protection

3. **Automatické přesměrování:**
   - Všechny HTTP požadavky → HTTPS
   - 301 Moved Permanently status

## Kompatibilita

- **Zpětná kompatibilita:** Zachována pro development (`--disable-https`)
- **Produkční nasazení:** Vyžaduje SSL certifikáty
- **Docker:** Funguje s existující konfigurací
- **Systemd:** Aktualizovaná služba pro HTTPS

## Testování

```bash
# Kompilace
go build cmd/api/main.go

# Test parametrů
./main --help

# Test HTTP módu
./main --disable-https --http-port=8080

# Test HTTPS (s certifikáty)
./main --cert=certs/cert.pem --key=certs/key.pem
```
