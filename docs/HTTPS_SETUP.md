# HTTPS Nastavení pro T.J. Sokol Suchdol

Tento dokument popisuje, jak nastavit HTTPS pro webovou aplikaci T.J. Sokol Suchdol.

## Možnosti HTTPS

### 1. Produk<PERSON><PERSON><PERSON> nasazení s Let's Encrypt (doporučeno)

Let's Encrypt poskytuje bezplatné SSL certifikáty s automatickým obnovením.

#### Automatická instalace

```bash
# Na serveru spusťte jako root:
sudo ./scripts/setup-letsencrypt.sh sokolsuchdol.cz
```

#### Manuální instalace

1. **Instalace Certbot:**
   ```bash
   sudo apt-get update
   sudo apt-get install certbot
   ```

2. **Získání certifikátu:**
   ```bash
   sudo certbot certonly --standalone \
     --email <EMAIL> \
     --agree-tos \
     --domains sokolsuchdol.cz,www.sokolsuchdol.cz
   ```

3. **Aktualizace systemd služby:**
   Certifikáty budou automaticky uloženy v `/etc/letsencrypt/live/sokolsuchdol.cz/`

4. **Restart aplikace:**
   ```bash
   sudo systemctl restart app.service
   ```

### 2. Vývojové prostředí s self-signed certifikáty

Pro lokální vývoj můžete použít self-signed certifikáty:

```bash
# Vygenerování certifikátů
make generate-certs

# Spuštění s HTTPS
make run-https
```

## Parametry aplikace

Aplikace podporuje následující parametry pro HTTPS:

- `--http-port=80` - Port pro HTTP server
- `--https-port=443` - Port pro HTTPS server  
- `--cert=/path/to/cert.pem` - Cesta k SSL certifikátu
- `--key=/path/to/key.pem` - Cesta k privátnímu klíči
- `--https-only` - Spustit pouze HTTPS (bez HTTP redirect)
- `--disable-https` - Zakázat HTTPS (pouze pro vývoj)

## Příklady spuštění

### Produkce (HTTPS + HTTP redirect)
```bash
./api --http-port=80 --https-port=443 \
  --cert=/etc/letsencrypt/live/sokolsuchdol.cz/fullchain.pem \
  --key=/etc/letsencrypt/live/sokolsuchdol.cz/privkey.pem
```

### Pouze HTTPS (bez HTTP)
```bash
./api --https-only --https-port=443 \
  --cert=/etc/letsencrypt/live/sokolsuchdol.cz/fullchain.pem \
  --key=/etc/letsencrypt/live/sokolsuchdol.cz/privkey.pem
```

### Vývoj (pouze HTTP)
```bash
./api --disable-https --http-port=8080
```

## Automatické obnovení certifikátů

Let's Encrypt certifikáty jsou platné 90 dní. Automatické obnovení je nastaveno pomocí cron:

```bash
# Kontrola nastavení cron
sudo crontab -l

# Manuální test obnovení
sudo certbot renew --dry-run
```

## Bezpečnostní konfigurace

Aplikace používá následující TLS konfiguraci:

- **Minimální verze TLS:** 1.2
- **Podporované cipher suites:**
  - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
  - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305  
  - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
- **Podporované křivky:** P-521, P-384, P-256

## Troubleshooting

### Certifikát nenalezen
```
Certificate file not found: /etc/letsencrypt/live/sokolsuchdol.cz/fullchain.pem
```
**Řešení:** Zkontrolujte, zda byl certifikát správně vygenerován pomocí `sudo ls -la /etc/letsencrypt/live/`

### Oprávnění k certifikátu
```
Permission denied: /etc/letsencrypt/live/sokolsuchdol.cz/privkey.pem
```
**Řešení:** Nastavte správná oprávnění:
```bash
sudo chmod 644 /etc/letsencrypt/live/sokolsuchdol.cz/fullchain.pem
sudo chmod 600 /etc/letsencrypt/live/sokolsuchdol.cz/privkey.pem
```

### Port 443 již používán
```
bind: address already in use
```
**Řešení:** Zkontrolujte, zda na portu 443 neběží jiná služba:
```bash
sudo netstat -tlnp | grep :443
sudo systemctl stop nginx  # pokud běží nginx
```

## Kontrola HTTPS

Po nastavení můžete otestovat HTTPS:

```bash
# Test připojení
curl -I https://sokolsuchdol.cz

# Kontrola certifikátu
openssl s_client -connect sokolsuchdol.cz:443 -servername sokolsuchdol.cz

# Test SSL hodnocení
https://www.ssllabs.com/ssltest/analyze.html?d=sokolsuchdol.cz
```
